{"name": "predictive-maintenance-service", "version": "1.0.0", "description": "基于AI的预测性维护服务", "main": "dist/main.js", "scripts": {"build": "tsc", "build:clean": "rimraf dist && tsc", "start": "node dist/main.js", "start:dev": "ts-node src/main.ts", "start:debug": "ts-node --inspect src/main.ts", "start:prod": "node dist/main.js", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\""}, "dependencies": {"@nestjs/common": "^10.3.0", "@nestjs/core": "^10.3.0", "@nestjs/platform-express": "^10.3.0", "@nestjs/websockets": "^10.3.0", "@nestjs/platform-socket.io": "^10.3.0", "@nestjs/typeorm": "^10.0.1", "@nestjs/config": "^3.1.1", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.1.17", "@nestjs/terminus": "^10.2.0", "@nestjs/axios": "^3.0.1", "@nestjs/cache-manager": "^2.1.1", "@nestjs/microservices": "^10.3.0", "typeorm": "^0.3.17", "mysql2": "^3.6.0", "redis": "^4.6.7", "cache-manager-redis-yet": "^4.1.2", "cache-manager": "^5.2.4", "socket.io": "^4.7.2", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "rxjs": "^7.8.1", "reflect-metadata": "^0.1.13", "uuid": "^9.0.0", "moment": "^2.29.4", "lodash": "^4.17.21", "ml-regression": "^6.0.1", "ml-matrix": "^6.10.4", "simple-statistics": "^7.8.3", "@tensorflow/tfjs": "^4.10.0", "node-cron": "^3.0.2"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.3.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/uuid": "^9.0.2", "@types/lodash": "^4.14.195", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3", "rimraf": "^5.0.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}